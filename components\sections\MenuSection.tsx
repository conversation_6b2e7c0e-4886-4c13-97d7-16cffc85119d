"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Container,
} from "@mui/material";

const burgers = [
  {
    name: "Classic Burger",
    price: "$8.99",
    description:
      "Juicy beef patty, fresh lettuce, tomatoes, onions, pickles, and our special house sauce.",
    image:
      "https://framerusercontent.com/images/k073DeYNt8s9bXDGbNg46jmMBIc.png",
  },
  {
    name: "Cheese Burger",
    price: "$8.99",
    description:
      "Juicy beef patty topped with melted cheddar cheese, fresh lettuce, tomatoes, onions, pickles, and ketchup on a toasted bun.",
    image: "https://framerusercontent.com/images/qeP1aHI5Pcu9IHkIcZqyx9q38.jpg",
  },
  {
    name: "Bacon BBQ Burger",
    price: "$8.99",
    description:
      "Beef patty with crispy bacon, cheddar cheese, onion rings, BBQ sauce, lettuce, and tomatoes on a toasted bun.",
    image:
      "https://framerusercontent.com/images/9PFEWzfMQAYfcFy9zs0EIsEMzik.jpg",
  },
  {
    name: "Spicy Jalapeño Burger",
    price: "$10.49",
    description:
      "Spicy beef patty, pepper jack cheese, jalapeños, lettuce, tomatoes, and chipotle mayo on a toasted bun.",
    image: "https://framerusercontent.com/images/c99PZqydYt9KObteJyOo1DdAA.jpg",
  },
];

export default function CategorySection() {
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.3 }
    );
    if (sectionRef.current) observer.observe(sectionRef.current);
    return () => observer.disconnect();
  }, []);

  return (
    <Container sx={{ py: 8 }}>
      <Box
        ref={sectionRef}
        sx={{
          opacity: inView ? 1 : 0,
          transform: inView
            ? "perspective(1200px) translate3d(0, 0, 0) scale(1)"
            : "perspective(1200px) translateY(40px) scale(0.98)",
          transition: "opacity 0.6s ease, transform 0.6s ease",
        }}
      >
        <Grid container spacing={4} position="relative">
          {/* Left Side Image */}
          <Grid item xs={12} md={5}>
            <Box
              sx={{
                position: "relative",
                height: { xs: 300, md: 463 },
                borderRadius: "32px",
                overflow: "hidden",
                "@media (min-width:810px) and (max-width:1199px)": {
                  aspectRatio: "0.7664041994750657",
                  height: "381px",
                  width: "40%",
                },
              }}
            >
              <CardMedia
                component="img"
                image="https://framerusercontent.com/images/k073DeYNt8s9bXDGbNg46jmMBIc.png"
                alt="Category"
                sx={{
                  position: "absolute",
                  inset: 0,
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                  objectPosition: "center",
                  borderRadius: "inherit",
                  imageRendering: "auto",
                }}
              />
            </Box>
          </Grid>

          {/* Burger List */}
          <Grid item xs={12} md={7}>
            <Typography variant="h4" fontWeight={700} mb={3}>
              Burgers
            </Typography>

            <Grid container spacing={3}>
              {burgers.map((item, idx) => (
                <Grid item xs={12} key={idx}>
                  <Card
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      gap: "24px",
                      padding: "20px",
                      borderRadius: "24px",
                      background: "#fffefc",
                      boxShadow: "0px 8px 30px rgba(232, 245, 186, 0.3)",
                      transition: "transform 0.3s ease",
                      "&:hover": {
                        transform: "translateY(-6px)",
                      },
                    }}
                  >
                    <CardMedia
                      component="img"
                      image={item.image}
                      alt={item.name}
                      sx={{
                        width: 100,
                        height: 100,
                        borderRadius: "50%",
                        objectFit: "cover",
                      }}
                    />
                    <CardContent sx={{ px: 0 }}>
                      <Typography variant="subtitle1" fontWeight={700} mb={0.5}>
                        {item.price}
                      </Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {item.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" mt={1}>
                        {item.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
}
