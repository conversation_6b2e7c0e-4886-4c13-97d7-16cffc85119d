"use client";
import React, { useState, useEffect } from "react";
import {
  AppBar,
  Box,
  Container,
  IconButton,
  Typography,
  List,
  ListItem,
  ListItemText,
  Button,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Home, Info, Phone, Utensils } from "lucide-react";

const navItems = [
  { name: "Home", icon: Home },
  { name: "Menu", icon: Utensils },
  { name: "About", icon: Info },
  { name: "Contact", icon: Phone },
];

export default function Header() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const toggleMenu = () => setMenuOpen((prev) => !prev);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      setScrolled(isScrolled);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          zIndex: 1300,
          background: "transparent",
          borderRadius: scrolled ? "0 0 24px 24px" : "0 0 32px 32px",
          transition: "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
          transform: scrolled ? "translateY(0)" : "translateY(0)",
          height: scrolled ? "70px" : "80px",
          overflow: "hidden",
        }}
      >
        {/* Enhanced Glass Layers */}
        {/* Primary Glass Layer - Backdrop Blur */}
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            backdropFilter: scrolled ? "blur(16px)" : "blur(12px)",
            filter: "saturate(120%) brightness(1.15)",
            transition: "all 0.4s ease",
            zIndex: 0,
          }}
        />

        {/* Secondary Glass Layer - Color Tint */}
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            background: scrolled
              ? "linear-gradient(135deg, rgba(250, 250, 250, 0.85) 0%, rgba(240, 240, 240, 0.9) 100%)"
              : "linear-gradient(135deg, rgba(250, 250, 250, 0.75) 0%, rgba(240, 240, 240, 0.8) 100%)",
            transition: "all 0.4s ease",
            zIndex: 1,
          }}
        />

        {/* Tertiary Glass Layer - Inner Glow & Border */}
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            zIndex: 2,
            boxShadow: scrolled
              ? `
                inset 1px 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 0 8px rgba(255, 255, 255, 0.15),
                0 8px 32px rgba(45, 93, 63, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.08)
              `
              : `
                inset 1px 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 0 5px rgba(255, 255, 255, 0.1),
                0 4px 20px rgba(45, 93, 63, 0.12),
                0 1px 4px rgba(0, 0, 0, 0.05)
              `,
            borderRadius: "inherit",
            transition: "all 0.4s ease",
            border: "1px solid rgba(255, 255, 255, 0.2)",
          }}
        />

        {/* Afghan Pattern Overlay */}
        {/* <Box
          sx={{
            position: "absolute",
            inset: 0,
            zIndex: 3,
            background: `
              radial-gradient(circle at 20% 20%, rgba(212, 148, 26, 0.08) 1px, transparent 1px),
              radial-gradient(circle at 80% 80%, rgba(212, 148, 26, 0.08) 1px, transparent 1px),
              linear-gradient(45deg, rgba(212, 148, 26, 0.03) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(212, 148, 26, 0.03) 25%, transparent 25%)
            `,
            backgroundSize: "16px 16px, 16px 16px, 32px 32px, 32px 32px",
            opacity: scrolled ? 0.6 : 0.4,
            transition: "opacity 0.4s ease",
            borderRadius: "inherit",
          }}
        /> */}

        <Container
          maxWidth="xl"
          sx={{
            position: "relative",
            zIndex: 4,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            minHeight: scrolled ? "70px" : "80px",
            transition: "all 0.4s ease",
            px: { xs: 2, sm: 3, md: 4 },
          }}
        >
          {/* Logo Section */}
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                onClick={toggleMenu}
                sx={{
                  height: 48,
                  width: 48,
                  borderRadius: "12px",
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  backdropFilter: "blur(8px)",
                  border: "1px solid rgba(255, 255, 255, 0.2)",
                  transform: menuOpen ? "rotate(90deg)" : "rotate(0deg)",
                  transition: "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.08)",
                    transform: menuOpen
                      ? "rotate(90deg) scale(1.05)"
                      : "scale(1.05)",
                  },
                }}
              >
                <Box
                  sx={{
                    width: 20,
                    height: 14,
                    position: "relative",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between",
                  }}
                >
                  <Box
                    sx={{
                      height: 2,
                      backgroundColor: "secondary.main",
                      borderRadius: "1px",
                      transition:
                        "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                      transform: menuOpen
                        ? "translateY(6px) rotate(45deg)"
                        : "translateY(0px) rotate(0deg)",
                    }}
                  />
                  <Box
                    sx={{
                      height: 2,
                      backgroundColor: "secondary.main",
                      borderRadius: "1px",
                      transition:
                        "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                      opacity: menuOpen ? 0 : 1,
                      transform: menuOpen ? "scale(0)" : "scale(1)",
                    }}
                  />
                  <Box
                    sx={{
                      height: 2,
                      backgroundColor: "secondary.main",
                      borderRadius: "1px",
                      transition:
                        "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                      transform: menuOpen
                        ? "translateY(-6px) rotate(-45deg)"
                        : "translateY(0px) rotate(0deg)",
                    }}
                  />
                </Box>
              </IconButton>
            )}

            {/* Restaurant Logo */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1.5 }}>
              <Box
                sx={{
                  width: scrolled ? 40 : 48,
                  height: scrolled ? 40 : 48,
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, #000 0%, #4F9C6E 100%)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "all 0.4s ease",
                  boxShadow: "0 4px 12px rgba(45, 93, 63, 0.3)",
                }}
              >
                <Utensils
                  size={scrolled ? 20 : 24}
                  color="#FFFFFF"
                  style={{ transition: "all 0.4s ease" }}
                />
              </Box>
              <Typography
                variant="h5"
                sx={{
                  color: "secondary.main",
                  fontWeight: 700,
                  fontSize: scrolled ? "1.4rem" : "1.6rem",
                  transition: "all 0.4s ease",
                  fontFamily: "'Inter', sans-serif",
                  letterSpacing: "-0.02em",
                  textShadow: "0 1px 2px rgba(45, 93, 63, 0.1)",
                }}
              >
                Polo
              </Typography>
            </Box>
          </Box>

          {/* Desktop Navigation */}
          {!isMobile && (
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {navItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <Button
                    key={item.name}
                    variant="text"
                    startIcon={<IconComponent size={18} />}
                    sx={{
                      color: "secondary.main",
                      fontWeight: 500,
                      fontSize: "0.95rem",
                      textTransform: "capitalize",
                      borderRadius: "12px",
                      px: 2.5,
                      py: 1,
                      mx: 0.5,
                      position: "relative",
                      overflow: "hidden",
                      transition:
                        "all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                      "&::before": {
                        content: '""',
                        position: "absolute",
                        top: 0,
                        left: "-100%",
                        width: "100%",
                        height: "100%",
                        background:
                          "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)",
                        transition: "left 0.6s ease",
                      },
                      "&:hover": {
                        backgroundColor: "rgba(45, 93, 63, 0.08)",
                        transform: "translateY(-2px)",
                        boxShadow: "0 4px 12px rgba(45, 93, 63, 0.15)",
                        "&::before": {
                          left: "100%",
                        },
                        "& .MuiButton-startIcon": {
                          transform: "scale(1.1)",
                        },
                      },
                      "&:active": {
                        transform: "translateY(0)",
                      },
                      "& .MuiButton-startIcon": {
                        transition: "transform 0.3s ease",
                        marginRight: "6px",
                      },
                    }}
                  >
                    {item.name}
                  </Button>
                );
              })}

              {/* CTA Button */}
              <Button
                variant="contained"
                sx={{
                  ml: 2,
                  backgroundColor: "secondary.main",
                  color: "#FFFFFF",
                  fontWeight: 600,
                  fontSize: "0.9rem",
                  textTransform: "capitalize",
                  borderRadius: "16px",
                  px: 3,
                  py: 1.2,
                  position: "relative",
                  overflow: "hidden",
                  boxShadow: "0 4px 12px rgba(45, 93, 63, 0.3)",
                  transition: "all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    width: "0",
                    height: "0",
                    borderRadius: "50%",
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                    transform: "translate(-50%, -50%)",
                    transition: "width 0.3s ease, height 0.3s ease",
                  },
                  "&:hover": {
                    backgroundColor: "#4F9C6E",
                    transform: "translateY(-2px)",
                    boxShadow: "0 8px 20px rgba(45, 93, 63, 0.4)",
                    "&::before": {
                      width: "120%",
                      height: "120%",
                    },
                  },
                  "&:active": {
                    transform: "translateY(0)",
                  },
                }}
              >
                Reserve Table
              </Button>
            </Box>
          )}
        </Container>
      </AppBar>

      {/* Enhanced Mobile Menu */}
      {isMobile && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: menuOpen ? "100vh" : "0",
            overflow: "hidden",
            // background:
            //   "linear-gradient(135deg, rgba(243, 255, 207, 0.98) 0%, rgba(232, 245, 186, 0.98) 100%)",
            backdropFilter: "blur(20px)",
            transition: "height 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
            zIndex: 1200,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {/* Afghan Pattern Overlay for Mobile Menu */}
          {/* <Box
            sx={{
              position: "absolute",
              inset: 0,
              background: `
                radial-gradient(circle at 25% 25%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(212, 148, 26, 0.1) 2px, transparent 2px),
                linear-gradient(45deg, rgba(212, 148, 26, 0.05) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(212, 148, 26, 0.05) 25%, transparent 25%)
              `,
              backgroundSize: "20px 20px, 20px 20px, 40px 40px, 40px 40px",
              opacity: 0.6,
              pointerEvents: "none",
            }}
          /> */}

          <List
            disablePadding
            sx={{
              position: "relative",
              zIndex: 1,
              width: "100%",
              maxWidth: "300px",
              px: 3,
            }}
          >
            {navItems.map((item, i) => {
              const IconComponent = item.icon;
              return (
                <ListItem
                  key={item.name}
                  sx={{
                    mb: 2,
                    borderRadius: "16px",
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    backdropFilter: "blur(8px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    transform: menuOpen
                      ? "scale(1) translateY(0px)"
                      : "scale(0.9) translateY(-20px)",
                    opacity: menuOpen ? 1 : 0,
                    transition: "all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                    transitionDelay: `${menuOpen ? 0.2 + i * 0.1 : 0}s`,
                    "&:hover": {
                      backgroundColor: "rgba(45, 93, 63, 0.1)",
                      transform: "scale(1.02) translateY(-2px)",
                      boxShadow: "0 8px 20px rgba(45, 93, 63, 0.15)",
                    },
                  }}
                >
                  <ListItemText
                    primary={
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <Box
                          sx={{
                            width: 40,
                            height: 40,
                            borderRadius: "10px",
                            backgroundColor: "secondary.main",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            boxShadow: "0 4px 8px rgba(45, 93, 63, 0.3)",
                          }}
                        >
                          <IconComponent size={20} color="#FFFFFF" />
                        </Box>
                        <Typography
                          component="a"
                          href="#"
                          sx={{
                            color: "secondary.main",
                            fontSize: "1.2rem",
                            textDecoration: "none",
                            fontWeight: 600,
                            fontFamily: "'Inter', sans-serif",
                            letterSpacing: "-0.01em",
                          }}
                        >
                          {item.name}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              );
            })}

            {/* Mobile CTA Button */}
            <Box sx={{ mt: 4, px: 2 }}>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  backgroundColor: "secondary.main",
                  color: "#FFFFFF",
                  fontWeight: 600,
                  fontSize: "1.1rem",
                  textTransform: "capitalize",
                  borderRadius: "16px",
                  py: 1.5,
                  boxShadow: "0 6px 16px rgba(45, 93, 63, 0.4)",
                  transform: menuOpen
                    ? "scale(1) translateY(0px)"
                    : "scale(0.9) translateY(-20px)",
                  opacity: menuOpen ? 1 : 0,
                  transition: "all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
                  transitionDelay: menuOpen ? "0.6s" : "0s",
                  "&:hover": {
                    backgroundColor: "#4F9C6E",
                    transform: "scale(1.02) translateY(-2px)",
                    boxShadow: "0 8px 20px rgba(45, 93, 63, 0.5)",
                  },
                }}
              >
                Reserve Table
              </Button>
            </Box>
          </List>
        </Box>
      )}

      {/* Dynamic Offset for fixed header */}
      <Box
        sx={{
          height: scrolled ? "70px" : "80px",
          transition: "height 0.4s ease",
        }}
      />
    </>
  );
}
